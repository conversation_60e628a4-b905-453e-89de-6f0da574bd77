# Sample Configuration for Retrieval MLM
# Based on the original retrieval_mlm.py settings

# Model Architecture Configuration
model:
  # Core architecture parameters
  block_size: 1024              # Maximum sequence length
  # vocab_size: determined dynamically from tokenizer
  n_layer: 12                   # Number of transformer layers
  n_head: 12                    # Number of attention heads
  n_embd: 768                   # Embedding dimension
  n_head_4: 12                  # Additional head parameter

  # MLM-specific parameters
  mask_prob: 0.3 #0.2                # Base masking probability

  # Time-mixing architecture parameters
  time_mixing:
    d_mix_lora_attention: 28    # LoRA dimension for attention time-mixing
    d_mix_lora_mlp: 32          # LoRA dimension for MLP time-mixing

  # Model precision and compilation
  dtype: "bfloat16"             # Model precision
  use_compile: true             # Use torch.compile for optimization

# Training Configuration
training:
  # Batch and sequence parameters
  total_batch_size: 1048576     # Total batch size in tokens (2**20, ~1M)
  micro_batch_size: 64          # Micro batch size (B)
  sequence_length: 1024         # Sequence length (T)

  # Learning rate schedule
  max_lr: 2.0e-4               # Maximum learning rate
  min_lr_ratio: 0.1            # Minimum LR as ratio of max_lr
  warmup_steps: 750            # Number of warmup steps
  max_steps: 9500              # Total training steps

  # Optimizer parameters
  base_learning_rate: 6.0e-4   # Base learning rate for optimizer setup
  weight_decay: 0.1            # Weight decay coefficient
  embedding_lr_scale: 0.1      # Learning rate scale for embedding parameters
  beta1: 0.9                   # Adam beta1
  beta2: 0.95                  # Adam beta2
  eps: 1.0e-8                  # Adam epsilon

  # Gradient and training stability
  grad_clip_norm: 1.0          # Gradient clipping norm

  # Evaluation and checkpointing
  val_eval_interval: 250       # Steps between validation evaluations
  val_loss_steps: 20           # Number of validation batches to evaluate
  checkpoint_interval: 5000    # Steps between checkpoint saves

  # Random seed
  random_seed: 1337            # Random seed for reproducibility

# Data Configuration
data:
  # Data loading mode
  tokenize_on_the_fly: false   # true: load raw text from Arrow files, false: load pre-tokenized .npy files

  # Pre-tokenized data paths (used when tokenize_on_the_fly: false)
  # Update these paths to match your tokenized data location
  # Pattern should match the output from tokenize_npy.py
  train_data_pattern: "/s2_nfs/retrieval_mlm/custom_tokenized/*_train_*.npy"
  val_data_pattern: "/s2_nfs/retrieval_mlm/custom_tokenized/*_val_*.npy"

  # Arrow data paths (used when tokenize_on_the_fly: true)
  # arrow_data_paths:
  #   - "/s2_nfs/fineweb-edu/localds/sample-10BT/"  # List of Arrow dataset paths
  target_tokens: 10000000000     # Target number of tokens for Arrow data loading

  # Tokenizer configuration
  # IMPORTANT: This must match the tokenizer used when creating .npy files
  # For .npy mode, ensure this matches the tokenizer used in tokenize_npy.py
  tokenizer_name: "/s2_nfs/tokenizer_spm_50368_140B_EnFa_hf_edited-cls" # "/s2_nfs/tooka_tokenizer" # "gpt2"       # Tokenizer to use (tiktoken encoding name or path)

  # MLM data processing
  mask_prob: 0.3               # Actual masking probability used in data loader

  # Performance optimization
  prefetch_enabled: true       # Enable background prefetching of .npy files (reduces GPU idle time)

# Infrastructure Configuration
infrastructure:
  # Device and distributed training
  device_type: "cuda"          # Device type (cuda, cpu, mps)

  # Logging and checkpoints
  log_dir: "/s2_nfs/Retrieval_MLM/logs"
  log_file_name: "log_standard_edufineweb.txt"

  # Logging verbosity control
  verbose_logging: false       # Reduce verbose output in distributed training

  # TensorBoard logging (legacy - will be replaced by wandb)
  tensorboard:
    enabled: false
    writer_dir_path: "./run_logs"
    run_group_name: "encoder_mlm_124M_edufineweb"
    run_number: "test"

# Weights & Biases Configuration
wandb:
  # Project configuration
  enabled: true                # Whether to use wandb logging
  project: "retrieval-mlm"     # W&B project name
  entity: null                 # W&B entity (team/username), null for default
  name: "mlm_30_custom_tok"  # Run name for wandb, log files, and checkpoints
  # group: "encoder_mlm_124M"    # Run group for organizing experiments
  tags: ["mlm", "retrieval", "encoder", "edufineweb"]  # List of tags for the run
  notes: "Retrieval MLM training on EduFineWeb dataset"  # Notes for the run

  # Logging configuration
  log_interval: 1              # Log metrics every N steps
  log_gradients: false         # Whether to log gradient histograms
  log_parameters: false        # Whether to log parameter histograms
  log_model: false             # Whether to log model artifacts

  # Sync configuration for DDP
  sync_tensorboard: false      # Sync tensorboard logs to wandb

  # Additional wandb settings
  save_code: true              # Save code snapshot
  resume: "allow"              # Resume behavior (allow, must, never, auto)

# Advanced Configuration
advanced:
  # Memory and performance
  set_float32_matmul_precision: "high"  # PyTorch matmul precision

  # DDP specific settings
  ddp:
    find_unused_parameters: false        # DDP find_unused_parameters setting (set to false to avoid warnings)
    backend: "nccl"                     # DDP backend

  # Model initialization
  init:
    std: 0.02                          # Standard deviation for weight initialization
    scale_init_layers: true            # Whether to scale initialization for specific layers