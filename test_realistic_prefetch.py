#!/usr/bin/env python3
"""
Test prefetching with realistic file sizes and batch configurations.
"""

import os
import sys
import time
import tempfile
import numpy as np

sys.path.insert(0, '.')

def create_realistic_test_files(temp_dir, num_files=3, tokens_per_file=1000000):
    """Create test .npy files with realistic sizes"""
    files = []
    for i in range(num_files):
        # Create realistic token data
        tokens = np.random.randint(0, 50000, size=tokens_per_file, dtype=np.uint16)
        
        filename = os.path.join(temp_dir, f"realistic_train_{i:06d}.npy")
        np.save(filename, tokens)
        files.append(filename)
        print(f"Created test file: {filename} with {tokens_per_file:,} tokens")
    
    return files

def test_realistic_prefetching():
    """Test prefetching with realistic batch sizes"""
    print("🚀 Testing Realistic Prefetching Scenario")
    print("=" * 60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create realistic test files (1M tokens each, smaller for testing)
        test_files = create_realistic_test_files(temp_dir, num_files=4, tokens_per_file=1000000)
        
        # Realistic config matching your setup
        config = {
            'data': {
                'tokenize_on_the_fly': False,
                'train_data_pattern': f"{temp_dir}/*.npy",
                'val_data_pattern': f"{temp_dir}/*.npy",
                'tokenizer_name': 'gpt2',
                'mask_prob': 0.3,
                'prefetch_enabled': True
            }
        }
        
        import retrieval_mlm_configurable as mlm
        
        config_obj = mlm.Config.__new__(mlm.Config)
        config_obj.config = config
        tokenizer_info = mlm.get_tokenizer_info('gpt2')
        
        print(f"\n📊 Test Configuration:")
        print(f"  Micro batch size: 64")
        print(f"  Sequence length: 1024") 
        print(f"  Num processes: 1 (simulated)")
        print(f"  File size: 1,000,000 tokens")
        print(f"  Tokens per batch: 64 * 1024 = 65,536")
        print(f"  Batches per file: ~15")
        
        # Create data loader with realistic settings
        data_loader = mlm.DataLoaderLite(
            B=64, T=1024, process_rank=0, num_processes=1,
            split='train', config=config_obj, tokenizer_info=tokenizer_info
        )
        
        print(f"\n🔄 Generating batches to trigger file switches...")
        
        prefetch_hits = 0
        file_switches = 0
        
        # Generate enough batches to trigger multiple file switches
        for batch_num in range(50):  # Should trigger ~3 file switches
            start_time = time.time()
            inputs, labels, attention_mask = data_loader.next_batch()
            batch_time = time.time() - start_time
            
            if batch_num % 5 == 0:
                print(f"  Batch {batch_num+1:2d}: {batch_time*1000:.1f}ms")
                
        data_loader.cleanup()
        
        print(f"\n✅ Test completed successfully")
        print(f"📊 Look for these messages in the output above:")
        print(f"  - '🚀 Starting prefetch for' (prefetch initiated)")
        print(f"  - '✅ Using prefetched file' (prefetch success)")
        print(f"  - '🚀 Early prefetch trigger' (early prefetch)")

def main():
    """Run realistic prefetching test"""
    print("🧪 Realistic Prefetching Test")
    print("=" * 60)
    
    try:
        test_realistic_prefetching()
        print("\n🎉 Test completed!")
        print("💡 If you see prefetch messages above, the optimization is working")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
