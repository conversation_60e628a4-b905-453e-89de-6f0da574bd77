2025-09-17 11:34:22,099 INFO    MainThread:349334 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-09-17 11:34:22,099 INFO    MainThread:349334 [wandb_setup.py:_flush():80] Configure stats pid to 349334
2025-09-17 11:34:22,099 INFO    MainThread:349334 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-09-17 11:34:22,099 INFO    MainThread:349334 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/Retrieval_MLM/wandb/settings
2025-09-17 11:34:22,099 INFO    MainThread:349334 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-09-17 11:34:22,099 INFO    MainThread:349334 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /home/<USER>/Retrieval_MLM/wandb/run-20250917_113422-3kygt4cu/logs/debug.log
2025-09-17 11:34:22,099 INFO    MainThread:349334 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /home/<USER>/Retrieval_MLM/wandb/run-20250917_113422-3kygt4cu/logs/debug-internal.log
2025-09-17 11:34:22,099 INFO    MainThread:349334 [wandb_init.py:init():830] calling init triggers
2025-09-17 11:34:22,100 INFO    MainThread:349334 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'model_block_size': 1024, 'model_n_layer': 12, 'model_n_head': 12, 'model_n_embd': 768, 'model_n_head_4': 12, 'model_mask_prob': 0.3, 'model_time_mixing_d_mix_lora_attention': 28, 'model_time_mixing_d_mix_lora_mlp': 32, 'model_dtype': 'bfloat16', 'model_use_compile': True, 'training_total_batch_size': 1048576, 'training_micro_batch_size': 64, 'training_sequence_length': 1024, 'training_max_lr': 0.0002, 'training_min_lr_ratio': 0.1, 'training_warmup_steps': 750, 'training_max_steps': 9500, 'training_base_learning_rate': 0.0006, 'training_weight_decay': 0.1, 'training_embedding_lr_scale': 0.1, 'training_beta1': 0.9, 'training_beta2': 0.95, 'training_eps': 1e-08, 'training_grad_clip_norm': 1.0, 'training_val_eval_interval': 250, 'training_val_loss_steps': 20, 'training_checkpoint_interval': 5000, 'training_random_seed': 1337, 'data_tokenize_on_the_fly': False, 'data_train_data_pattern': '/s2_nfs/retrieval_mlm/custom_tokenized/*_train_*.npy', 'data_val_data_pattern': '/s2_nfs/retrieval_mlm/custom_tokenized/*_val_*.npy', 'data_target_tokens': 10000000000, 'data_tokenizer_name': '/s2_nfs/tokenizer_spm_50368_140B_EnFa_hf_edited-cls', 'data_mask_prob': 0.3, 'data_prefetch_enabled': True, 'infra_device_type': 'cuda', 'infra_log_dir': '/s2_nfs/Retrieval_MLM/logs', 'infra_log_file_name': 'log_standard_edufineweb.txt', 'infra_verbose_logging': False, 'infra_tensorboard_enabled': False, 'infra_tensorboard_writer_dir_path': './run_logs', 'infra_tensorboard_run_group_name': 'encoder_mlm_124M_edufineweb', 'infra_tensorboard_run_number': 'test', 'model_parameters': 165720576, '_wandb': {'code_path': 'code/retrieval_mlm_configurable.py'}}
2025-09-17 11:34:22,100 INFO    MainThread:349334 [wandb_init.py:init():871] starting backend
2025-09-17 11:34:22,304 INFO    MainThread:349334 [wandb_init.py:init():874] sending inform_init request
2025-09-17 11:34:22,307 INFO    MainThread:349334 [wandb_init.py:init():882] backend started and connected
2025-09-17 11:34:22,308 INFO    MainThread:349334 [wandb_init.py:init():953] updated telemetry
2025-09-17 11:34:22,309 INFO    MainThread:349334 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-09-17 11:34:23,670 INFO    MainThread:349334 [wandb_init.py:init():1029] starting run threads in backend
