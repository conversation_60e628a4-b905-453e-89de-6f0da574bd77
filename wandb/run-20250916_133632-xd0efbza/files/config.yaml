_wandb:
    value:
        cli_version: 0.21.0
        code_path: code/retrieval_mlm_configurable.py
        e:
            vkte0n0s9c14jznru68qrcbv0tbyyoou:
                args:
                    - --config
                    - config.yaml
                codePath: retrieval_mlm_configurable.py
                codePathLocal: retrieval_mlm_configurable.py
                cpu_count: 112
                cpu_count_logical: 112
                cudaVersion: "12.8"
                disk:
                    /:
                        total: "1511024451584"
                        used: "1437440544768"
                email: <EMAIL>
                executable: /opt/anaconda3/envs/llm_env/bin/python3.12
                gpu: NVIDIA H100 80GB HBM3
                gpu_count: 8
                gpu_nvidia:
                    - architecture: Hopper
                      cudaCores: 16896
                      memoryTotal: "85520809984"
                      name: NVIDIA H100 80GB HBM3
                      uuid: GPU-77194a63-0847-1f11-ca76-93b042e00e4e
                    - architecture: Hopper
                      cudaCores: 16896
                      memoryTotal: "85520809984"
                      name: NVIDIA H100 80GB HBM3
                      uuid: GPU-a4339cc5-9120-434c-0efd-72e07743bd78
                    - architecture: Hopper
                      cudaCores: 16896
                      memoryTotal: "85520809984"
                      name: NVIDIA H100 80GB HBM3
                      uuid: GPU-23269b3b-5279-4644-f19a-9271181482ea
                    - architecture: Hopper
                      cudaCores: 16896
                      memoryTotal: "85520809984"
                      name: NVIDIA H100 80GB HBM3
                      uuid: GPU-ba724632-f670-4b14-dafd-acce0f0e9962
                    - architecture: Hopper
                      cudaCores: 16896
                      memoryTotal: "85520809984"
                      name: NVIDIA H100 80GB HBM3
                      uuid: GPU-488886e3-ab3f-2a65-b7cf-3e53717dd5d0
                    - architecture: Hopper
                      cudaCores: 16896
                      memoryTotal: "85520809984"
                      name: NVIDIA H100 80GB HBM3
                      uuid: GPU-83acd5d5-35cc-9096-017a-4ca54c2b2e4e
                    - architecture: Hopper
                      cudaCores: 16896
                      memoryTotal: "85520809984"
                      name: NVIDIA H100 80GB HBM3
                      uuid: GPU-2db63487-870f-eb77-f005-971e9a97ef57
                    - architecture: Hopper
                      cudaCores: 16896
                      memoryTotal: "85520809984"
                      name: NVIDIA H100 80GB HBM3
                      uuid: GPU-347d855e-f066-042d-3ee4-3a8d5584a599
                host: h82
                memory:
                    total: "1651351687168"
                os: Linux-5.15.0-153-generic-x86_64-with-glibc2.35
                program: /home/<USER>/Retrieval_MLM/retrieval_mlm_configurable.py
                python: CPython 3.12.9
                root: /home/<USER>/Retrieval_MLM
                startedAt: "2025-09-16T10:06:32.526291Z"
                writerId: vkte0n0s9c14jznru68qrcbv0tbyyoou
        m: []
        python_version: 3.12.9
        t:
            "1":
                - 1
                - 5
                - 11
                - 49
                - 51
                - 53
                - 71
                - 105
            "2":
                - 1
                - 5
                - 11
                - 49
                - 51
                - 53
                - 71
                - 105
            "3":
                - 13
                - 15
                - 16
                - 61
            "4": 3.12.9
            "5": 0.21.0
            "6": 4.55.0
            "12": 0.21.0
            "13": linux-x86_64
data_arrow_data_paths:
    value:
        - /s2_nfs/fineweb-edu/localds/sample-10BT/
data_mask_prob:
    value: 0.3
data_target_tokens:
    value: 10000000000
data_tokenize_on_the_fly:
    value: true
data_tokenizer_name:
    value: /s2_nfs/tokenizer_spm_50368_140B_EnFa_hf_edited-cls
infra_device_type:
    value: cuda
infra_log_dir:
    value: /s2_nfs/Retrieval_MLM/logs
infra_log_file_name:
    value: log_standard_edufineweb.txt
infra_tensorboard_enabled:
    value: false
infra_tensorboard_run_group_name:
    value: encoder_mlm_124M_edufineweb
infra_tensorboard_run_number:
    value: test
infra_tensorboard_writer_dir_path:
    value: ./run_logs
infra_verbose_logging:
    value: false
model_block_size:
    value: 1024
model_dtype:
    value: bfloat16
model_mask_prob:
    value: 0.3
model_n_embd:
    value: 768
model_n_head:
    value: 12
model_n_head_4:
    value: 12
model_n_layer:
    value: 12
model_parameters:
    value: 165726720
model_time_mixing_d_mix_lora_attention:
    value: 28
model_time_mixing_d_mix_lora_mlp:
    value: 32
model_use_compile:
    value: true
training_base_learning_rate:
    value: 0.0006
training_beta1:
    value: 0.9
training_beta2:
    value: 0.95
training_checkpoint_interval:
    value: 5000
training_embedding_lr_scale:
    value: 0.1
training_eps:
    value: 1e-08
training_grad_clip_norm:
    value: 1
training_max_lr:
    value: 0.0002
training_max_steps:
    value: 9500
training_micro_batch_size:
    value: 64
training_min_lr_ratio:
    value: 0.1
training_random_seed:
    value: 1337
training_sequence_length:
    value: 1024
training_total_batch_size:
    value: 1048576
training_val_eval_interval:
    value: 250
training_val_loss_steps:
    value: 20
training_warmup_steps:
    value: 750
training_weight_decay:
    value: 0.1
