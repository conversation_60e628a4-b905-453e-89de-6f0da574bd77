{"time":"2025-09-16T12:39:58.566119811+03:30","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-09-16T12:39:59.443939595+03:30","level":"INFO","msg":"stream: created new stream","id":"ye9u2u3g"}
{"time":"2025-09-16T12:39:59.444560899+03:30","level":"INFO","msg":"stream: started","id":"ye9u2u3g"}
{"time":"2025-09-16T12:39:59.444586408+03:30","level":"INFO","msg":"writer: Do: started","stream_id":"ye9u2u3g"}
{"time":"2025-09-16T12:39:59.4445921+03:30","level":"INFO","msg":"handler: started","stream_id":"ye9u2u3g"}
{"time":"2025-09-16T12:39:59.444655352+03:30","level":"INFO","msg":"sender: started","stream_id":"ye9u2u3g"}
{"time":"2025-09-16T12:40:00.29332363+03:30","level":"ERROR","msg":"git repo not found","error":"repository does not exist"}
{"time":"2025-09-16T12:45:53.313861079+03:30","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/axiomlaborg-axiom-lab/retrieval-mlm/ye9u2u3g/file_stream\": unexpected EOF"}
{"time":"2025-09-16T13:08:37.830639447+03:30","level":"INFO","msg":"stream: closing","id":"ye9u2u3g"}
{"time":"2025-09-16T13:08:40.078016934+03:30","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-09-16T13:08:40.721967936+03:30","level":"INFO","msg":"handler: closed","stream_id":"ye9u2u3g"}
{"time":"2025-09-16T13:08:40.722013601+03:30","level":"INFO","msg":"writer: Close: closed","stream_id":"ye9u2u3g"}
{"time":"2025-09-16T13:08:40.722042277+03:30","level":"INFO","msg":"sender: closed","stream_id":"ye9u2u3g"}
{"time":"2025-09-16T13:08:40.722094989+03:30","level":"INFO","msg":"stream: closed","id":"ye9u2u3g"}
