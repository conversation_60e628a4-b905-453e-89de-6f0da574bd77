### starting training loop of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on dataset @2025-09-16 13:29:24.073683
Total trainable parameters: 165_726_720
torch.compile: True
=> Batch_size:64; Sequence_length:1024; max_lr:0.0002; min_lr_ratio:0.1
Step 0: validation loss (MLM): 11.0389
step     0 | loss: 11.042561 | lr 2.6667e-07 | norm: 29.7117 | dt: 31821.51ms | tok/sec: 32951.80...
step     1 | loss: 11.031266 | lr 5.3333e-07 | norm: 29.9763 | dt: 710.90ms | tok/sec: 1474999.23...
step     2 | loss: 10.989562 | lr 8.0000e-07 | norm: 28.4201 | dt: 1674.26ms | tok/sec: 626293.50...
step     3 | loss: 10.924165 | lr 1.0667e-06 | norm: 29.1169 | dt: 722.75ms | tok/sec: 1450821.53...
step     4 | loss: 10.837279 | lr 1.3333e-06 | norm: 28.5213 | dt: 706.48ms | tok/sec: 1484228.47...
step     5 | loss: 10.746581 | lr 1.6000e-06 | norm: 26.0410 | dt: 704.36ms | tok/sec: 1488701.33...
step     6 | loss: 10.650100 | lr 1.8667e-06 | norm: 23.7833 | dt: 714.00ms | tok/sec: 1468600.24...
step     7 | loss: 10.566170 | lr 2.1333e-06 | norm: 19.9247 | dt: 736.92ms | tok/sec: 1422914.48...
step     8 | loss: 10.487273 | lr 2.4000e-06 | norm: 17.4090 | dt: 722.13ms | tok/sec: 1452054.96...
step     9 | loss: 10.390457 | lr 2.6667e-06 | norm: 15.7257 | dt: 719.31ms | tok/sec: 1457757.31...
step    10 | loss: 10.318810 | lr 2.9333e-06 | norm: 14.0146 | dt: 766.43ms | tok/sec: 1368121.40...
step    11 | loss: 10.305818 | lr 3.2000e-06 | norm: 17.3839 | dt: 705.59ms | tok/sec: 1486090.61...
step    12 | loss: 10.214341 | lr 3.4667e-06 | norm: 11.2737 | dt: 720.41ms | tok/sec: 1455521.20...
step    13 | loss: 10.116750 | lr 3.7333e-06 | norm: 10.8892 | dt: 734.15ms | tok/sec: 1428279.41...
step    14 | loss: 10.069958 | lr 4.0000e-06 | norm: 10.1948 | dt: 697.93ms | tok/sec: 1502398.08...
step    15 | loss: 10.026839 | lr 4.2667e-06 | norm: 9.4894 | dt: 695.67ms | tok/sec: 1507287.03...
step    16 | loss: 10.022132 | lr 4.5333e-06 | norm: 8.2355 | dt: 757.98ms | tok/sec: 1383386.74...
step    17 | loss: 9.916268 | lr 4.8000e-06 | norm: 8.3622 | dt: 713.45ms | tok/sec: 1469722.63...
step    18 | loss: 9.902017 | lr 5.0667e-06 | norm: 7.5348 | dt: 701.99ms | tok/sec: 1493720.53...
step    19 | loss: 9.802571 | lr 5.3333e-06 | norm: 7.2008 | dt: 711.83ms | tok/sec: 1473080.40...
step    20 | loss: 9.827133 | lr 5.6000e-06 | norm: 6.5229 | dt: 700.08ms | tok/sec: 1497794.20...
step    21 | loss: 9.863677 | lr 5.8667e-06 | norm: 7.6870 | dt: 746.00ms | tok/sec: 1405603.60...
step    22 | loss: 9.792412 | lr 6.1333e-06 | norm: 5.9239 | dt: 717.69ms | tok/sec: 1461047.94...
step    23 | loss: 9.754749 | lr 6.4000e-06 | norm: 5.9692 | dt: 738.55ms | tok/sec: 1419767.06...
step    24 | loss: 9.750813 | lr 6.6667e-06 | norm: 5.0084 | dt: 723.58ms | tok/sec: 1449140.27...
step    25 | loss: 9.721971 | lr 6.9333e-06 | norm: 4.5265 | dt: 702.30ms | tok/sec: 1493059.29...
step    26 | loss: 9.668049 | lr 7.2000e-06 | norm: 4.1335 | dt: 736.93ms | tok/sec: 1422907.11...
step    27 | loss: 9.647968 | lr 7.4667e-06 | norm: 4.0374 | dt: 747.91ms | tok/sec: 1402009.11...
step    28 | loss: 9.624322 | lr 7.7333e-06 | norm: 3.6666 | dt: 767.84ms | tok/sec: 1365609.52...
step    29 | loss: 9.678310 | lr 8.0000e-06 | norm: 3.6351 | dt: 731.18ms | tok/sec: 1434080.91...
step    30 | loss: 9.613289 | lr 8.2667e-06 | norm: 3.8109 | dt: 709.94ms | tok/sec: 1476984.08...
step    31 | loss: 9.643638 | lr 8.5333e-06 | norm: 3.4703 | dt: 717.99ms | tok/sec: 1460425.97...
step    32 | loss: 9.661691 | lr 8.8000e-06 | norm: 3.4353 | dt: 711.27ms | tok/sec: 1474221.51...
step    33 | loss: 9.587872 | lr 9.0667e-06 | norm: 3.4651 | dt: 710.43ms | tok/sec: 1475968.45...
step    34 | loss: 9.583389 | lr 9.3333e-06 | norm: 3.1461 | dt: 772.52ms | tok/sec: 1357348.45...
Traceback (most recent call last):
  File "/home/<USER>/Retrieval_MLM/retrieval_mlm_configurable.py", line 1554, in <module>
    _, loss = model(x, attention_mask=attention_mask, labels=labels)
    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/cuda/__init__.py", line 1040, in synchronize
    return torch._C._cuda_synchronize()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
KeyboardInterrupt
[rank0]: Traceback (most recent call last):
[rank0]:   File "/home/<USER>/Retrieval_MLM/retrieval_mlm_configurable.py", line 1554, in <module>
[rank0]:     _, loss = model(x, attention_mask=attention_mask, labels=labels)
[rank0]: ^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/cuda/__init__.py", line 1040, in synchronize
[rank0]:     return torch._C._cuda_synchronize()
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]: KeyboardInterrupt
