2025-09-16 13:29:22,178 INFO    MainThread:132268 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-09-16 13:29:22,178 INFO    MainThread:132268 [wandb_setup.py:_flush():80] Configure stats pid to 132268
2025-09-16 13:29:22,178 INFO    MainThread:132268 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-09-16 13:29:22,178 INFO    MainThread:132268 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/Retrieval_MLM/wandb/settings
2025-09-16 13:29:22,178 INFO    MainThread:132268 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-09-16 13:29:22,178 INFO    MainThread:132268 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /home/<USER>/Retrieval_MLM/wandb/run-20250916_132922-s8os54mq/logs/debug.log
2025-09-16 13:29:22,178 INFO    MainThread:132268 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /home/<USER>/Retrieval_MLM/wandb/run-20250916_132922-s8os54mq/logs/debug-internal.log
2025-09-16 13:29:22,178 INFO    MainThread:132268 [wandb_init.py:init():830] calling init triggers
2025-09-16 13:29:22,178 INFO    MainThread:132268 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'model_block_size': 1024, 'model_n_layer': 12, 'model_n_head': 12, 'model_n_embd': 768, 'model_n_head_4': 12, 'model_mask_prob': 0.3, 'model_time_mixing_d_mix_lora_attention': 28, 'model_time_mixing_d_mix_lora_mlp': 32, 'model_dtype': 'bfloat16', 'model_use_compile': True, 'training_total_batch_size': 1048576, 'training_micro_batch_size': 64, 'training_sequence_length': 1024, 'training_max_lr': 0.0002, 'training_min_lr_ratio': 0.1, 'training_warmup_steps': 750, 'training_max_steps': 9500, 'training_base_learning_rate': 0.0006, 'training_weight_decay': 0.1, 'training_embedding_lr_scale': 0.1, 'training_beta1': 0.9, 'training_beta2': 0.95, 'training_eps': 1e-08, 'training_grad_clip_norm': 1.0, 'training_val_eval_interval': 250, 'training_val_loss_steps': 20, 'training_checkpoint_interval': 5000, 'training_random_seed': 1337, 'data_tokenize_on_the_fly': True, 'data_arrow_data_paths': ['/s2_nfs/fineweb-edu/localds/sample-10BT/'], 'data_target_tokens': 10000000000, 'data_tokenizer_name': '/s2_nfs/tokenizer_spm_50368_140B_EnFa_hf_edited-cls', 'data_mask_prob': 0.3, 'infra_device_type': 'cuda', 'infra_log_dir': '/s2_nfs/Retrieval_MLM/logs', 'infra_log_file_name': 'log_standard_edufineweb.txt', 'infra_verbose_logging': False, 'infra_tensorboard_enabled': False, 'infra_tensorboard_writer_dir_path': './run_logs', 'infra_tensorboard_run_group_name': 'encoder_mlm_124M_edufineweb', 'infra_tensorboard_run_number': 'test', 'model_parameters': 165726720, '_wandb': {'code_path': 'code/retrieval_mlm_configurable.py'}}
2025-09-16 13:29:22,179 INFO    MainThread:132268 [wandb_init.py:init():871] starting backend
2025-09-16 13:29:22,385 INFO    MainThread:132268 [wandb_init.py:init():874] sending inform_init request
2025-09-16 13:29:22,424 INFO    MainThread:132268 [wandb_init.py:init():882] backend started and connected
2025-09-16 13:29:22,428 INFO    MainThread:132268 [wandb_init.py:init():953] updated telemetry
2025-09-16 13:29:22,428 INFO    MainThread:132268 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-09-16 13:29:23,992 INFO    MainThread:132268 [wandb_init.py:init():1029] starting run threads in backend
2025-09-16 13:29:24,071 INFO    MainThread:132268 [wandb_run.py:_console_start():2458] atexit reg
2025-09-16 13:29:24,071 INFO    MainThread:132268 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-09-16 13:29:24,071 INFO    MainThread:132268 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-09-16 13:29:24,072 INFO    MainThread:132268 [wandb_run.py:_redirect():2398] Redirects installed.
2025-09-16 13:29:24,073 INFO    MainThread:132268 [wandb_init.py:init():1075] run started, returning control to user process
2025-09-16 13:30:23,088 INFO    MsgRouterThr:132268 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 1 handles.
