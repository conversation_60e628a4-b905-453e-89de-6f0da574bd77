### starting training loop of En<PERSON>r<PERSON><PERSON><PERSON> on dataset @2025-09-17 10:44:37.024191
Total trainable parameters: 472_358_400
torch.compile: True
=> Batch_size:64; Sequence_length:1024; max_lr:0.0002; min_lr_ratio:0.1
Step 0: validation loss (MLM): 12.5450
Traceback (most recent call last):
  File "/home/<USER>/Retrieval_MLM/retrieval_mlm_configurable.py", line 1605, in <module>
    _, loss = model(x, attention_mask=attention_mask, labels=labels)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/parallel/distributed.py", line 1637, in forward
    else self._run_ddp_forward(*inputs, **kwargs)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/parallel/distributed.py", line 1464, in _run_ddp_forward
    return self.module(*inputs, **kwargs)  # type: ignore[index]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_dynamo/eval_frame.py", line 655, in _fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Retrieval_MLM/retrieval_mlm_configurable.py", line 554, in forward
    if mask_positions.any():
  File "/home/<USER>/Retrieval_MLM/retrieval_mlm_configurable.py", line 556, in torch_dynamo_resume_in_forward_at_554
    batch_indices, pos_indices = torch.where(mask_positions)
  File "/home/<USER>/Retrieval_MLM/retrieval_mlm_configurable.py", line 556, in torch_dynamo_resume_in_forward_at_556
    batch_indices, pos_indices = torch.where(mask_positions)
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_dynamo/eval_frame.py", line 838, in _fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/fx/graph_module.py", line 830, in call_wrapped
    return self._wrapped_call(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/fx/graph_module.py", line 406, in __call__
    raise e
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/fx/graph_module.py", line 393, in __call__
    return super(self.cls, obj).__call__(*args, **kwargs)  # type: ignore[misc]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<eval_with_key>.290", line 15, in forward
    submod_1 = self.compiled_submod_1(submod_0, s1, l_self_modules_lm_head_parameters_weight_, l_stack0_0_, l_stack0_1_, s3, l_labels_);  submod_0 = s1 = l_self_modules_lm_head_parameters_weight_ = l_stack0_0_ = l_stack0_1_ = s3 = l_labels_ = None
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_dynamo/backends/distributed.py", line 171, in forward
    x = self.submod(*args)
        ^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_dynamo/eval_frame.py", line 838, in _fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/aot_autograd.py", line 1201, in forward
    return compiled_fn(full_args)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/runtime_wrappers.py", line 315, in runtime_wrapper
    all_outs = call_func_at_runtime_with_args(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/utils.py", line 126, in call_func_at_runtime_with_args
    out = normalize_as_list(f(args))
                            ^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/utils.py", line 100, in g
    return f(*args)
           ^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/autograd/function.py", line 575, in apply
    return super().apply(*args, **kwargs)  # type: ignore[misc]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/runtime_wrappers.py", line 1937, in forward
    fw_outs = call_func_at_runtime_with_args(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/utils.py", line 126, in call_func_at_runtime_with_args
    out = normalize_as_list(f(args))
                            ^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/runtime_wrappers.py", line 598, in wrapper
    return compiled_fn(runtime_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/runtime_wrappers.py", line 495, in wrapper
    return compiled_fn(runtime_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/runtime_wrappers.py", line 689, in inner_fn
    outs = compiled_fn(args)
           ^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_inductor/output_code.py", line 460, in __call__
    return self.current_callable(inputs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_inductor/utils.py", line 2404, in run
    return model(new_inputs)
           ^^^^^^^^^^^^^^^^^
  File "/tmp/torchinductor_modernbert/ji/cjib5h5p65fbmsup6d6xrk5obhko44jauqw53rdgxjbcmgxvdoaz.py", line 424, in call
    buf2 = empty_strided_cuda((s1, 250002), (250002, 1), torch.bfloat16)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 9.09 GiB. GPU 0 has a total capacity of 79.19 GiB of which 1.56 GiB is free. Including non-PyTorch memory, this process has 77.56 GiB memory in use. Of the allocated memory 75.97 GiB is allocated by PyTorch, and 61.64 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
[rank0]: Traceback (most recent call last):
[rank0]:   File "/home/<USER>/Retrieval_MLM/retrieval_mlm_configurable.py", line 1605, in <module>
[rank0]:     _, loss = model(x, attention_mask=attention_mask, labels=labels)
[rank0]:               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/parallel/distributed.py", line 1637, in forward
[rank0]:     else self._run_ddp_forward(*inputs, **kwargs)
[rank0]:          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/parallel/distributed.py", line 1464, in _run_ddp_forward
[rank0]:     return self.module(*inputs, **kwargs)  # type: ignore[index]
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_dynamo/eval_frame.py", line 655, in _fn
[rank0]:     return fn(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/home/<USER>/Retrieval_MLM/retrieval_mlm_configurable.py", line 554, in forward
[rank0]:     if mask_positions.any():
[rank0]:   File "/home/<USER>/Retrieval_MLM/retrieval_mlm_configurable.py", line 556, in torch_dynamo_resume_in_forward_at_554
[rank0]:     batch_indices, pos_indices = torch.where(mask_positions)
[rank0]:   File "/home/<USER>/Retrieval_MLM/retrieval_mlm_configurable.py", line 556, in torch_dynamo_resume_in_forward_at_556
[rank0]:     batch_indices, pos_indices = torch.where(mask_positions)
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_dynamo/eval_frame.py", line 838, in _fn
[rank0]:     return fn(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/fx/graph_module.py", line 830, in call_wrapped
[rank0]:     return self._wrapped_call(self, *args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/fx/graph_module.py", line 406, in __call__
[rank0]:     raise e
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/fx/graph_module.py", line 393, in __call__
[rank0]:     return super(self.cls, obj).__call__(*args, **kwargs)  # type: ignore[misc]
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "<eval_with_key>.290", line 15, in forward
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_dynamo/backends/distributed.py", line 171, in forward
[rank0]:     x = self.submod(*args)
[rank0]:         ^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_dynamo/eval_frame.py", line 838, in _fn
[rank0]:     return fn(*args, **kwargs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/aot_autograd.py", line 1201, in forward
[rank0]:     return compiled_fn(full_args)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/runtime_wrappers.py", line 315, in runtime_wrapper
[rank0]:     all_outs = call_func_at_runtime_with_args(
[rank0]:                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/utils.py", line 126, in call_func_at_runtime_with_args
[rank0]:     out = normalize_as_list(f(args))
[rank0]:                             ^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/utils.py", line 100, in g
[rank0]:     return f(*args)
[rank0]:            ^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/autograd/function.py", line 575, in apply
[rank0]:     return super().apply(*args, **kwargs)  # type: ignore[misc]
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/runtime_wrappers.py", line 1937, in forward
[rank0]:     fw_outs = call_func_at_runtime_with_args(
[rank0]:               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/utils.py", line 126, in call_func_at_runtime_with_args
[rank0]:     out = normalize_as_list(f(args))
[rank0]:                             ^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/runtime_wrappers.py", line 598, in wrapper
[rank0]:     return compiled_fn(runtime_args)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/runtime_wrappers.py", line 495, in wrapper
[rank0]:     return compiled_fn(runtime_args)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_functorch/_aot_autograd/runtime_wrappers.py", line 689, in inner_fn
[rank0]:     outs = compiled_fn(args)
[rank0]:            ^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_inductor/output_code.py", line 460, in __call__
[rank0]:     return self.current_callable(inputs)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]:   File "/opt/anaconda3/envs/llm_env/lib/python3.12/site-packages/torch/_inductor/utils.py", line 2404, in run
[rank0]:     return model(new_inputs)
[rank0]:            ^^^^^^^^^^^^^^^^^
[rank0]:   File "/tmp/torchinductor_modernbert/ji/cjib5h5p65fbmsup6d6xrk5obhko44jauqw53rdgxjbcmgxvdoaz.py", line 424, in call
[rank0]:     buf2 = empty_strided_cuda((s1, 250002), (250002, 1), torch.bfloat16)
[rank0]:            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[rank0]: torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 9.09 GiB. GPU 0 has a total capacity of 79.19 GiB of which 1.56 GiB is free. Including non-PyTorch memory, this process has 77.56 GiB memory in use. Of the allocated memory 75.97 GiB is allocated by PyTorch, and 61.64 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
