"""
Enhanced tokenization script for various datasets and tokenizers.
Supports both tiktoken encodings (e.g., "gpt2") and custom tokenizer paths.
Downloads and tokenizes data and saves data shards to disk.

Usage examples:
$ python tokenize_npy.py --tokenizer_name gpt2 --dataset_source "HuggingFaceFW/fineweb-edu" --output_path "edu_fineweb10B"
$ python tokenize_npy.py --tokenizer_name "/path/to/custom/tokenizer" --dataset_source "local/path/to/data" --shard_size 50000000
"""

import os
import argparse
import multiprocessing as mp
import numpy as np
import tiktoken
from datasets import load_dataset # pip install datasets
from tqdm import tqdm # pip install tqdm
from pathlib import Path

def get_tokenizer_and_info(tokenizer_name):
    """
    Get tokenizer and related information.
    Supports both tiktoken encodings and custom tokenizer paths.
    """
    tokenizer_info = {
        'tokenizer': None,
        'vocab_size': None,
        'eot_token': None,
        'is_custom': False
    }

    # Check if it's a tiktoken encoding name or custom path
    if tokenizer_name.startswith('/') or '\\' in tokenizer_name or os.path.exists(tokenizer_name):
        # Custom tokenizer path
        try:
            from transformers import AutoTokenizer
            tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)

            tokenizer_info['tokenizer'] = tokenizer
            tokenizer_info['vocab_size'] = len(tokenizer.vocab) if hasattr(tokenizer, 'vocab') else tokenizer.vocab_size
            tokenizer_info['is_custom'] = True

            # For custom tokenizers, we'll use EOS token as document delimiter
            tokenizer_info['eot_token'] = getattr(tokenizer, 'eos_token_id', None)
            if tokenizer_info['eot_token'] is None:
                # Fallback: use a special token or add one
                tokenizer_info['eot_token'] = tokenizer_info['vocab_size']  # Use next available ID
                print(f"Warning: No EOS token found, using token ID {tokenizer_info['eot_token']} as document delimiter")

            print(f"Loaded custom tokenizer from {tokenizer_name}")
            print(f"  Vocab size: {tokenizer_info['vocab_size']}")
            print(f"  Document delimiter token ID: {tokenizer_info['eot_token']}")

        except Exception as e:
            print(f"Error loading custom tokenizer from {tokenizer_name}: {e}")
            print("Falling back to tiktoken gpt2 encoding")
            tokenizer_name = "gpt2"  # Fallback

    # Handle tiktoken encodings (including fallback)
    if not tokenizer_info['tokenizer']:
        try:
            tokenizer = tiktoken.get_encoding(tokenizer_name)
            tokenizer_info['tokenizer'] = tokenizer
            tokenizer_info['vocab_size'] = tokenizer.n_vocab
            tokenizer_info['is_custom'] = False
            tokenizer_info['eot_token'] = tokenizer._special_tokens['<|endoftext|>']

            print(f"Loaded tiktoken encoding: {tokenizer_name}")
            print(f"  Vocab size: {tokenizer_info['vocab_size']}")
            print(f"  Document delimiter token ID: {tokenizer_info['eot_token']}")

        except Exception as e:
            raise ValueError(f"Could not load tokenizer '{tokenizer_name}': {e}")

    return tokenizer_info

def create_tokenize_function(tokenizer_info):
    """Create tokenization function based on tokenizer type"""
    def tokenize(doc):
        # tokenizes a single document and returns a numpy array of uint16 tokens
        tokens = [tokenizer_info['eot_token']]  # document delimiter token

        if tokenizer_info['is_custom']:
            # Custom tokenizer (transformers)
            text_tokens = tokenizer_info['tokenizer'].encode(doc["text"], add_special_tokens=False)
        else:
            # tiktoken tokenizer
            text_tokens = tokenizer_info['tokenizer'].encode_ordinary(doc["text"])

        tokens.extend(text_tokens)
        tokens_np = np.array(tokens)

        # Check if we need uint16 or uint32 based on vocab size
        if tokenizer_info['vocab_size'] < 2**16:
            assert (0 <= tokens_np).all() and (tokens_np < 2**16).all(), "token dictionary too large for uint16"
            tokens_np_typed = tokens_np.astype(np.uint16)
        else:
            assert (0 <= tokens_np).all() and (tokens_np < 2**32).all(), "token dictionary too large for uint32"
            tokens_np_typed = tokens_np.astype(np.uint32)

        return tokens_np_typed

    return tokenize

def write_datafile(filename, tokens_np):
    """Save tokenized data to .npy file"""
    np.save(filename, tokens_np)

def load_dataset_from_source(dataset_source, dataset_name=None):
    """Load dataset from various sources"""
    if os.path.exists(dataset_source):
        # Local dataset path
        print(f"Loading dataset from local path: {dataset_source}")
        return load_dataset(dataset_source, split="train")
    else:
        # HuggingFace dataset
        print(f"Loading dataset from HuggingFace: {dataset_source}")
        if dataset_name:
            return load_dataset(dataset_source, name=dataset_name, split="train")
        else:
            return load_dataset(dataset_source, split="train")

def parse_arguments():
    """Parse command-line arguments"""
    parser = argparse.ArgumentParser(description="Tokenize datasets and save as .npy shards")

    parser.add_argument(
        "--tokenizer_name",
        type=str,
        default="gpt2",
        help="Tokenizer name (tiktoken encoding like 'gpt2') or path to custom tokenizer"
    )

    parser.add_argument(
        "--dataset_source",
        type=str,
        default="HuggingFaceFW/fineweb-edu",
        help="Dataset source: HuggingFace dataset name or local path"
    )

    parser.add_argument(
        "--dataset_name",
        type=str,
        default="sample-10BT",
        help="Dataset configuration name (for HuggingFace datasets with multiple configs)"
    )

    parser.add_argument(
        "--output_path",
        type=str,
        default="tokenized_data",
        help="Output directory for tokenized .npy files"
    )

    parser.add_argument(
        "--shard_size",
        type=int,
        default=100_000_000,
        help="Number of tokens per shard (default: 100M)"
    )

    parser.add_argument(
        "--sequence_length",
        type=int,
        default=1024,
        help="Sequence length parameter (for future use)"
    )

    parser.add_argument(
        "--num_processes",
        type=int,
        default=None,
        help="Number of processes for multiprocessing (default: half of CPU count)"
    )

    return parser.parse_args()

def main():
    """Main tokenization function"""
    args = parse_arguments()

    print("=" * 60)
    print("Enhanced Dataset Tokenization Script")
    print("=" * 60)
    print(f"Tokenizer: {args.tokenizer_name}")
    print(f"Dataset source: {args.dataset_source}")
    print(f"Dataset name: {args.dataset_name}")
    print(f"Output path: {args.output_path}")
    print(f"Shard size: {args.shard_size:,} tokens")
    print(f"Sequence length: {args.sequence_length}")
    print("=" * 60)

    # Create output directory
    DATA_CACHE_DIR = os.path.join(os.path.dirname(__file__), args.output_path)
    os.makedirs(DATA_CACHE_DIR, exist_ok=True)
    print(f"Output directory: {DATA_CACHE_DIR}")

    # Load tokenizer
    print("\nLoading tokenizer...")
    tokenizer_info = get_tokenizer_and_info(args.tokenizer_name)
    tokenize_func = create_tokenize_function(tokenizer_info)

    # Load dataset
    print("\nLoading dataset...")
    try:
        if args.dataset_source == "HuggingFaceFW/fineweb-edu":
            # Special handling for fineweb-edu with dataset_name
            fw = load_dataset(args.dataset_source, name=args.dataset_name, split="train")
        else:
            fw = load_dataset_from_source(args.dataset_source, args.dataset_name)
        print(f"Dataset loaded successfully. Total examples: {len(fw):,}")
    except Exception as e:
        print(f"Error loading dataset: {e}")
        return

    # Determine data type based on vocab size
    dtype = np.uint16 if tokenizer_info['vocab_size'] < 2**16 else np.uint32
    print(f"Using data type: {dtype}")

    # Set up multiprocessing
    nprocs = args.num_processes if args.num_processes else max(1, os.cpu_count()//2)
    print(f"Using {nprocs} processes for tokenization")

    print("\nStarting tokenization...")

    # Tokenize all documents and write output shards
    with mp.Pool(nprocs) as pool:
        shard_index = 0
        # Preallocate buffer to hold current shard
        all_tokens_np = np.empty((args.shard_size,), dtype=dtype)
        token_count = 0
        progress_bar = None
        total_tokens_processed = 0

        for tokens in pool.imap(tokenize_func, fw, chunksize=16):
            # Is there enough space in the current shard for the new tokens?
            if token_count + len(tokens) < args.shard_size:
                # Simply append tokens to current shard
                all_tokens_np[token_count:token_count+len(tokens)] = tokens
                token_count += len(tokens)
                total_tokens_processed += len(tokens)

                # Update progress bar
                if progress_bar is None:
                    progress_bar = tqdm(total=args.shard_size, unit="tokens", desc=f"Shard {shard_index}")
                progress_bar.update(len(tokens))
            else:
                # Write the current shard and start a new one
                split = "val" if shard_index == 0 else "train"

                # Create filename with dataset info
                dataset_prefix = Path(args.output_path).name
                filename = os.path.join(DATA_CACHE_DIR, f"{dataset_prefix}_{split}_{shard_index:06d}")

                # Split the document into whatever fits in this shard; remainder goes to next one
                remainder = args.shard_size - token_count
                progress_bar.update(remainder)
                all_tokens_np[token_count:token_count+remainder] = tokens[:remainder]
                write_datafile(filename, all_tokens_np)

                print(f"\nWrote shard {shard_index}: {filename}.npy ({args.shard_size:,} tokens)")
                shard_index += 1
                progress_bar = None

                # Populate the next shard with the leftovers of the current doc
                all_tokens_np[0:len(tokens)-remainder] = tokens[remainder:]
                token_count = len(tokens)-remainder
                total_tokens_processed += len(tokens)

        # Write any remaining tokens as the last shard
        if token_count != 0:
            split = "val" if shard_index == 0 else "train"
            dataset_prefix = Path(args.output_path).name
            filename = os.path.join(DATA_CACHE_DIR, f"{dataset_prefix}_{split}_{shard_index:06d}")
            write_datafile(filename, all_tokens_np[:token_count])
            print(f"\nWrote final shard {shard_index}: {filename}.npy ({token_count:,} tokens)")

    print(f"\nTokenization complete!")
    print(f"Total tokens processed: {total_tokens_processed:,}")
    print(f"Total shards created: {shard_index + 1}")
    print(f"Output directory: {DATA_CACHE_DIR}")

    # Save tokenization info
    info_file = os.path.join(DATA_CACHE_DIR, "tokenization_info.txt")
    with open(info_file, 'w') as f:
        f.write(f"Tokenization Info\n")
        f.write(f"================\n")
        f.write(f"Tokenizer: {args.tokenizer_name}\n")
        f.write(f"Dataset source: {args.dataset_source}\n")
        f.write(f"Dataset name: {args.dataset_name}\n")
        f.write(f"Vocab size: {tokenizer_info['vocab_size']}\n")
        f.write(f"Document delimiter token: {tokenizer_info['eot_token']}\n")
        f.write(f"Data type: {dtype}\n")
        f.write(f"Shard size: {args.shard_size:,} tokens\n")
        f.write(f"Total tokens: {total_tokens_processed:,}\n")
        f.write(f"Total shards: {shard_index + 1}\n")
        f.write(f"Sequence length: {args.sequence_length}\n")

    print(f"Tokenization info saved to: {info_file}")

if __name__ == "__main__":
    main()