#!/usr/bin/env python3
"""
Monitor script to check for prefetching activity in training logs.
"""

import sys
import time
import subprocess
import re
from datetime import datetime

def monitor_training_logs(log_pattern="*.log"):
    """Monitor training logs for prefetch-related messages"""
    print("🔍 Monitoring for prefetch activity...")
    print("Looking for patterns:")
    print("  - 'Using prefetched file' (prefetch success)")
    print("  - 'Starting prefetch for' (prefetch initiated)")
    print("  - 'File switch detected' (file transitions)")
    print("  - 'Loading .npy file' (sync loading)")
    print("=" * 60)
    
    # Patterns to look for
    patterns = {
        'prefetch_success': r'✅ Using prefetched file.*PREFETCH SUCCESS',
        'prefetch_start': r'🚀 Starting prefetch for',
        'file_switch': r'🔧 File switch detected',
        'sync_loading': r'Loading \.npy file:',
        'step': r'step\s+(\d+)',
    }
    
    compiled_patterns = {name: re.compile(pattern) for name, pattern in patterns.items()}
    
    # Statistics
    stats = {
        'prefetch_hits': 0,
        'prefetch_starts': 0,
        'file_switches': 0,
        'sync_loads': 0,
        'last_step': 0
    }
    
    try:
        # Use tail -f to follow the most recent log file
        # Adjust this command based on your log file location
        cmd = ["tail", "-f", "/s2_nfs/Retrieval_MLM/logs/log_standard_edufineweb.txt"]
        
        print(f"Running: {' '.join(cmd)}")
        print("Press Ctrl+C to stop monitoring")
        print("-" * 60)
        
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, 
                                 universal_newlines=True, bufsize=1)
        
        for line in iter(process.stdout.readline, ''):
            line = line.strip()
            if not line:
                continue
                
            timestamp = datetime.now().strftime("%H:%M:%S")
            
            # Check for step numbers
            step_match = compiled_patterns['step'].search(line)
            if step_match:
                stats['last_step'] = int(step_match.group(1))
            
            # Check for prefetch success
            if compiled_patterns['prefetch_success'].search(line):
                stats['prefetch_hits'] += 1
                print(f"[{timestamp}] 🎯 PREFETCH HIT #{stats['prefetch_hits']} (Step ~{stats['last_step']})")
                print(f"           {line}")
                
            # Check for prefetch start
            elif compiled_patterns['prefetch_start'].search(line):
                stats['prefetch_starts'] += 1
                print(f"[{timestamp}] 🚀 PREFETCH START #{stats['prefetch_starts']} (Step ~{stats['last_step']})")
                print(f"           {line}")
                
            # Check for file switches
            elif compiled_patterns['file_switch'].search(line):
                stats['file_switches'] += 1
                print(f"[{timestamp}] 🔄 FILE SWITCH #{stats['file_switches']} (Step ~{stats['last_step']})")
                print(f"           {line}")
                
            # Check for sync loading (fallback)
            elif compiled_patterns['sync_loading'].search(line):
                stats['sync_loads'] += 1
                print(f"[{timestamp}] 📁 SYNC LOAD #{stats['sync_loads']} (Step ~{stats['last_step']})")
                print(f"           {line}")
                
            # Show step progress occasionally
            elif step_match and stats['last_step'] % 50 == 0:
                print(f"[{timestamp}] 📊 Step {stats['last_step']} - Stats: Prefetch hits: {stats['prefetch_hits']}, Starts: {stats['prefetch_starts']}, Switches: {stats['file_switches']}")
                
    except KeyboardInterrupt:
        print("\n" + "=" * 60)
        print("📊 Final Statistics:")
        print(f"  Prefetch hits: {stats['prefetch_hits']}")
        print(f"  Prefetch starts: {stats['prefetch_starts']}")
        print(f"  File switches: {stats['file_switches']}")
        print(f"  Sync loads: {stats['sync_loads']}")
        print(f"  Last step: {stats['last_step']}")
        
        if stats['prefetch_hits'] > 0:
            print("✅ Prefetching is working!")
        elif stats['prefetch_starts'] > 0:
            print("⚠️ Prefetching is starting but not being used")
        elif stats['file_switches'] > 0:
            print("⚠️ File switches detected but no prefetching activity")
        else:
            print("❌ No prefetch activity detected")
            
    except FileNotFoundError:
        print("❌ Log file not found. Please check the log file path.")
        print("You may need to adjust the log file path in this script.")
        
    except Exception as e:
        print(f"❌ Error monitoring logs: {e}")

def estimate_file_switch_frequency():
    """Estimate how often file switches should occur"""
    print("📊 Estimating file switch frequency...")
    
    # Your current config
    micro_batch_size = 64
    sequence_length = 1024
    num_gpus = 8
    file_size_tokens = 100_000_000
    
    tokens_per_step = micro_batch_size * sequence_length * num_gpus
    steps_per_file = file_size_tokens // tokens_per_step
    
    print(f"  Tokens per step: {tokens_per_step:,}")
    print(f"  File size: {file_size_tokens:,} tokens")
    print(f"  Steps per file: ~{steps_per_file}")
    print(f"  File switch every ~{steps_per_file} steps")
    print()

def main():
    print("🔍 Prefetch Activity Monitor")
    print("=" * 60)
    
    estimate_file_switch_frequency()
    
    print("This script will monitor your training logs for prefetch activity.")
    print("Make sure your training is running before starting the monitor.")
    print()
    
    try:
        monitor_training_logs()
    except KeyboardInterrupt:
        print("\nMonitoring stopped.")

if __name__ == "__main__":
    main()
