#!/usr/bin/env python3
"""
Test script to verify that the prefetching optimization works correctly.
"""

import os
import sys
import time
import tempfile
import numpy as np
from pathlib import Path

# Add current directory to path
sys.path.insert(0, '.')

def create_test_npy_files(temp_dir, num_files=3, tokens_per_file=1000):
    """Create test .npy files for testing prefetching"""
    files = []
    for i in range(num_files):
        # Create some dummy token data
        tokens = np.random.randint(0, 1000, size=tokens_per_file, dtype=np.uint16)
        
        # Add some document delimiter tokens
        tokens[0] = 999  # Document delimiter
        tokens[tokens_per_file//2] = 999  # Another document
        
        filename = os.path.join(temp_dir, f"test_train_{i:06d}.npy")
        np.save(filename, tokens)
        files.append(filename)
        print(f"Created test file: {filename} with {tokens_per_file} tokens")
    
    return files

def test_prefetching_performance():
    """Test that prefetching improves performance"""
    print("🚀 Testing Prefetching Performance")
    print("=" * 60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create test files
        test_files = create_test_npy_files(temp_dir, num_files=5, tokens_per_file=10000)
        
        # Create test configs
        base_config = {
            'data': {
                'tokenize_on_the_fly': False,
                'train_data_pattern': f"{temp_dir}/*.npy",
                'val_data_pattern': f"{temp_dir}/*.npy",
                'tokenizer_name': 'gpt2',
                'mask_prob': 0.3,
                'prefetch_enabled': True  # Will test both True and False
            }
        }
        
        # Import after creating files
        import retrieval_mlm_configurable as mlm
        
        # Test with prefetching enabled
        print("\n📈 Testing WITH prefetching...")
        config_with_prefetch = mlm.Config.__new__(mlm.Config)
        config_with_prefetch.config = base_config
        
        tokenizer_info = mlm.get_tokenizer_info('gpt2')
        
        start_time = time.time()
        data_loader_prefetch = mlm.DataLoaderLite(
            B=4, T=64, process_rank=0, num_processes=1,
            split='train', config=config_with_prefetch, tokenizer_info=tokenizer_info
        )
        
        # Generate several batches to trigger file switches
        for i in range(20):
            batch = data_loader_prefetch.next_batch()
            if i % 5 == 0:
                print(f"  Generated batch {i+1}/20")
        
        prefetch_time = time.time() - start_time
        data_loader_prefetch.cleanup()
        
        # Test with prefetching disabled
        print("\n📉 Testing WITHOUT prefetching...")
        base_config['data']['prefetch_enabled'] = False
        config_no_prefetch = mlm.Config.__new__(mlm.Config)
        config_no_prefetch.config = base_config
        
        start_time = time.time()
        data_loader_no_prefetch = mlm.DataLoaderLite(
            B=4, T=64, process_rank=0, num_processes=1,
            split='train', config=config_no_prefetch, tokenizer_info=tokenizer_info
        )
        
        # Generate several batches to trigger file switches
        for i in range(20):
            batch = data_loader_no_prefetch.next_batch()
            if i % 5 == 0:
                print(f"  Generated batch {i+1}/20")
        
        no_prefetch_time = time.time() - start_time
        data_loader_no_prefetch.cleanup()
        
        # Results
        print("\n📊 Results:")
        print(f"  With prefetching:    {prefetch_time:.3f}s")
        print(f"  Without prefetching: {no_prefetch_time:.3f}s")
        
        if prefetch_time < no_prefetch_time:
            improvement = ((no_prefetch_time - prefetch_time) / no_prefetch_time) * 100
            print(f"  ✅ Prefetching improved performance by {improvement:.1f}%")
        else:
            print(f"  ⚠️ No significant improvement (files may be too small for testing)")
        
        return True

def test_prefetching_correctness():
    """Test that prefetching produces correct results"""
    print("\n🔍 Testing Prefetching Correctness")
    print("=" * 60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create test files with known patterns
        test_files = create_test_npy_files(temp_dir, num_files=3, tokens_per_file=200)
        
        base_config = {
            'data': {
                'tokenize_on_the_fly': False,
                'train_data_pattern': f"{temp_dir}/*.npy",
                'val_data_pattern': f"{temp_dir}/*.npy",
                'tokenizer_name': 'gpt2',
                'mask_prob': 0.3,
                'prefetch_enabled': True
            }
        }
        
        import retrieval_mlm_configurable as mlm
        
        config = mlm.Config.__new__(mlm.Config)
        config.config = base_config
        tokenizer_info = mlm.get_tokenizer_info('gpt2')
        
        # Test with prefetching
        data_loader = mlm.DataLoaderLite(
            B=2, T=32, process_rank=0, num_processes=1,
            split='train', config=config, tokenizer_info=tokenizer_info
        )
        
        # Generate batches and verify they contain valid data
        valid_batches = 0
        for i in range(10):
            inputs, labels, attention_mask = data_loader.next_batch()
            
            # Basic validation
            assert inputs.shape == (2, 32), f"Wrong input shape: {inputs.shape}"
            assert labels.shape == (2, 32), f"Wrong labels shape: {labels.shape}"
            assert attention_mask.shape == (2, 32), f"Wrong attention mask shape: {attention_mask.shape}"
            
            # Check that we have some real tokens (not all padding)
            real_tokens = (inputs != tokenizer_info['pad_token_id']).sum()
            if real_tokens > 0:
                valid_batches += 1
        
        data_loader.cleanup()
        
        print(f"  ✅ Generated {valid_batches}/10 valid batches")
        print(f"  ✅ All batches had correct shapes")
        print(f"  ✅ Prefetching maintains data integrity")
        
        return True

def main():
    """Run all prefetching tests"""
    print("🧪 Prefetching Optimization Tests")
    print("=" * 60)
    
    try:
        # Test performance
        success1 = test_prefetching_performance()
        
        # Test correctness
        success2 = test_prefetching_correctness()
        
        if success1 and success2:
            print("\n🎉 All prefetching tests passed!")
            print("✅ Prefetching optimization is working correctly")
            print("✅ Data integrity is maintained")
            print("\n💡 The prefetching should help reduce GPU idle time during training")
            return True
        else:
            print("\n❌ Some tests failed")
            return False
            
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
