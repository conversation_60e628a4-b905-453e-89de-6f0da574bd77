#!/usr/bin/env python3
"""
Usage examples for the enhanced tokenization and data loading pipeline.
Demonstrates how to use both tiktoken and custom tokenizers.
"""

import os
import subprocess
import sys
from pathlib import Path

def example_1_tiktoken_gpt2():
    """Example 1: Using tiktoken gpt2 encoding"""
    print("=" * 80)
    print("EXAMPLE 1: Tokenizing with tiktoken gpt2 encoding")
    print("=" * 80)
    
    print("Step 1: Tokenize data using gpt2 encoding")
    print("Command:")
    cmd = [
        "python", "tokenize_npy.py",
        "--tokenizer_name", "gpt2",
        "--dataset_source", "HuggingFaceFW/fineweb-edu",
        "--dataset_name", "sample-10BT",
        "--output_path", "edu_fineweb_gpt2",
        "--shard_size", "100000000",  # 100M tokens per shard
        "--sequence_length", "1024"
    ]
    print(" ".join(cmd))
    print()
    
    print("Step 2: Update config.yaml for .npy mode:")
    config_example = """
data:
  tokenize_on_the_fly: false
  train_data_pattern: "edu_fineweb_gpt2/*_train_*.npy"
  val_data_pattern: "edu_fineweb_gpt2/*_val_*.npy"
  tokenizer_name: "gpt2"  # Must match tokenization
  mask_prob: 0.3
"""
    print(config_example)
    
    print("Step 3: Run training:")
    print("python retrieval_mlm_configurable.py config.yaml")
    print()

def example_2_custom_tokenizer():
    """Example 2: Using custom tokenizer"""
    print("=" * 80)
    print("EXAMPLE 2: Tokenizing with custom tokenizer")
    print("=" * 80)
    
    print("Step 1: Tokenize data using custom tokenizer")
    print("Command:")
    cmd = [
        "python", "tokenize_npy.py",
        "--tokenizer_name", "/path/to/custom/tokenizer",
        "--dataset_source", "HuggingFaceFW/fineweb-edu",
        "--dataset_name", "sample-10BT",
        "--output_path", "edu_fineweb_custom",
        "--shard_size", "50000000",  # 50M tokens per shard
        "--sequence_length", "1024"
    ]
    print(" ".join(cmd))
    print()
    
    print("Step 2: Update config.yaml for .npy mode:")
    config_example = """
data:
  tokenize_on_the_fly: false
  train_data_pattern: "edu_fineweb_custom/*_train_*.npy"
  val_data_pattern: "edu_fineweb_custom/*_val_*.npy"
  tokenizer_name: "/path/to/custom/tokenizer"  # Must match tokenization
  mask_prob: 0.3
"""
    print(config_example)
    
    print("Step 3: Run training:")
    print("python retrieval_mlm_configurable.py config.yaml")
    print()

def example_3_local_dataset():
    """Example 3: Using local dataset"""
    print("=" * 80)
    print("EXAMPLE 3: Tokenizing local dataset")
    print("=" * 80)
    
    print("Step 1: Tokenize local data")
    print("Command:")
    cmd = [
        "python", "tokenize_npy.py",
        "--tokenizer_name", "gpt2",
        "--dataset_source", "/path/to/local/dataset",
        "--output_path", "local_data_tokenized",
        "--shard_size", "25000000",  # 25M tokens per shard
        "--sequence_length", "1024"
    ]
    print(" ".join(cmd))
    print()
    
    print("Note: Local dataset should be in HuggingFace datasets format")
    print("with a 'text' column containing the documents.")
    print()

def example_4_different_shard_sizes():
    """Example 4: Different shard sizes for different use cases"""
    print("=" * 80)
    print("EXAMPLE 4: Different shard sizes for different scenarios")
    print("=" * 80)
    
    scenarios = [
        ("Small dataset/testing", "1000000", "1M tokens - good for testing"),
        ("Medium dataset", "50000000", "50M tokens - good for medium datasets"),
        ("Large dataset", "100000000", "100M tokens - good for large datasets"),
        ("Very large dataset", "200000000", "200M tokens - for very large datasets")
    ]
    
    for scenario, shard_size, description in scenarios:
        print(f"{scenario}:")
        print(f"  --shard_size {shard_size}  # {description}")
        print()

def example_5_complete_workflow():
    """Example 5: Complete workflow from tokenization to training"""
    print("=" * 80)
    print("EXAMPLE 5: Complete workflow")
    print("=" * 80)
    
    workflow_steps = [
        "# Step 1: Tokenize your dataset",
        "python tokenize_npy.py \\",
        "    --tokenizer_name gpt2 \\",
        "    --dataset_source HuggingFaceFW/fineweb-edu \\",
        "    --dataset_name sample-10BT \\",
        "    --output_path my_tokenized_data \\",
        "    --shard_size 100000000",
        "",
        "# Step 2: Update config.yaml",
        "# Set tokenize_on_the_fly: false",
        "# Set train_data_pattern: 'my_tokenized_data/*_train_*.npy'",
        "# Set val_data_pattern: 'my_tokenized_data/*_val_*.npy'",
        "# Set tokenizer_name to match what you used in step 1",
        "",
        "# Step 3: Run training",
        "python retrieval_mlm_configurable.py config.yaml",
        "",
        "# Step 4: Monitor training with wandb (if enabled in config)",
        "# Check logs and metrics in your wandb dashboard"
    ]
    
    for step in workflow_steps:
        print(step)
    print()

def show_tokenizer_compatibility():
    """Show tokenizer compatibility information"""
    print("=" * 80)
    print("TOKENIZER COMPATIBILITY")
    print("=" * 80)
    
    print("Supported tokenizer types:")
    print("1. tiktoken encodings:")
    print("   - 'gpt2' (GPT-2 tokenizer)")
    print("   - 'cl100k_base' (GPT-3.5/GPT-4 tokenizer)")
    print("   - 'p50k_base' (Codex tokenizer)")
    print("   - 'r50k_base' (GPT-3 tokenizer)")
    print()
    
    print("2. Custom tokenizers (HuggingFace format):")
    print("   - Any path to a tokenizer directory")
    print("   - Must be loadable with AutoTokenizer.from_pretrained()")
    print("   - Examples:")
    print("     - '/path/to/my/tokenizer'")
    print("     - 'bert-base-uncased'")
    print("     - 'microsoft/DialoGPT-medium'")
    print()
    
    print("IMPORTANT: The tokenizer used in tokenize_npy.py MUST match")
    print("the tokenizer_name in config.yaml for training!")
    print()

def show_troubleshooting():
    """Show common troubleshooting tips"""
    print("=" * 80)
    print("TROUBLESHOOTING")
    print("=" * 80)
    
    issues = [
        ("No .npy files found", [
            "Check that train_data_pattern and val_data_pattern are correct",
            "Ensure tokenize_npy.py completed successfully",
            "Verify the output_path matches the pattern in config.yaml"
        ]),
        ("Token ID out of range errors", [
            "Ensure tokenizer_name in config.yaml matches tokenize_npy.py",
            "Check tokenization_info.txt for vocab size information",
            "Verify no tokenizer version mismatches"
        ]),
        ("Memory issues during tokenization", [
            "Reduce --shard_size parameter",
            "Reduce --num_processes parameter",
            "Process dataset in smaller chunks"
        ]),
        ("Slow tokenization", [
            "Increase --num_processes (up to CPU count)",
            "Use faster storage (SSD vs HDD)",
            "Consider using smaller dataset for testing"
        ])
    ]
    
    for issue, solutions in issues:
        print(f"Issue: {issue}")
        for solution in solutions:
            print(f"  - {solution}")
        print()

def main():
    """Show all examples and usage information"""
    print("🚀 Enhanced Tokenization Pipeline - Usage Examples")
    print()
    
    example_1_tiktoken_gpt2()
    example_2_custom_tokenizer()
    example_3_local_dataset()
    example_4_different_shard_sizes()
    example_5_complete_workflow()
    show_tokenizer_compatibility()
    show_troubleshooting()
    
    print("=" * 80)
    print("For more help:")
    print("  python tokenize_npy.py --help")
    print("  python test_pipeline.py  # Run tests")
    print("=" * 80)

if __name__ == "__main__":
    main()
