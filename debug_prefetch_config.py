#!/usr/bin/env python3
"""
Debug script to check if prefetching configuration is being read correctly.
"""

import sys
sys.path.insert(0, '.')

import retrieval_mlm_configurable as mlm

def main():
    print("🔍 Debugging Prefetch Configuration")
    print("=" * 50)
    
    # Load the actual config file
    try:
        config = mlm.Config('config.yaml')
        print("✅ Config loaded successfully")
        
        # Check prefetch setting
        prefetch_enabled = config.get('data.prefetch_enabled', 'NOT_SET')
        print(f"📋 prefetch_enabled setting: {prefetch_enabled}")
        print(f"📋 Type: {type(prefetch_enabled)}")
        
        # Check other data settings
        tokenize_on_the_fly = config.get('data.tokenize_on_the_fly', 'NOT_SET')
        print(f"📋 tokenize_on_the_fly: {tokenize_on_the_fly}")
        
        train_pattern = config.get('data.train_data_pattern', 'NOT_SET')
        print(f"📋 train_data_pattern: {train_pattern}")
        
        # Check if we're in .npy mode
        if not tokenize_on_the_fly:
            print("✅ In .npy mode - prefetching should be active")
        else:
            print("⚠️ In Arrow mode - prefetching not applicable")
            
        # Test tokenizer loading
        tokenizer_name = config.get('data.tokenizer_name', 'gpt2')
        print(f"📋 tokenizer_name: {tokenizer_name}")
        
        try:
            tokenizer_info = mlm.get_tokenizer_info(tokenizer_name)
            print(f"✅ Tokenizer loaded: vocab_size={tokenizer_info['vocab_size']}")
        except Exception as e:
            print(f"❌ Tokenizer loading failed: {e}")
            
        print("\n" + "=" * 50)
        print("🔧 Configuration Summary:")
        print(f"  Mode: {'NPY' if not tokenize_on_the_fly else 'Arrow'}")
        print(f"  Prefetch: {prefetch_enabled}")
        print(f"  Pattern: {train_pattern}")
        
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
