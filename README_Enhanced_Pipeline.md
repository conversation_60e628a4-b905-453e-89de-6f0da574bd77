# Enhanced Tokenization and Data Loading Pipeline

This document describes the enhanced tokenization and data loading pipeline for the Retrieval MLM project. The pipeline now supports flexible tokenization with both tiktoken encodings and custom tokenizers, along with improved .npy file loading compatibility.

## 🚀 Key Enhancements

### 1. Enhanced `tokenize_npy.py`
- **Command-line arguments**: Full configurability via CLI
- **Multiple tokenizer support**: Both tiktoken encodings and custom tokenizers
- **Flexible data sources**: HuggingFace datasets and local data
- **Configurable output**: Custom shard sizes and output paths
- **Validation**: Tokenization info files for compatibility checking

### 2. Improved `retrieval_mlm_configurable.py`
- **Fixed .npy loading**: Proper compatibility with pre-tokenized files
- **Tokenizer validation**: Automatic checking of tokenizer compatibility
- **Better error handling**: Clear error messages for mismatched tokenizers
- **Special token consistency**: Unified special token handling

### 3. Updated Configuration
- **Flexible config.yaml**: Support for both on-the-fly and pre-tokenized modes
- **Clear documentation**: Commented configuration options
- **Validation warnings**: Alerts for potential compatibility issues

## 📋 Usage Examples

### Basic Usage with tiktoken

```bash
# Step 1: Tokenize data
python tokenize_npy.py \
    --tokenizer_name gpt2 \
    --dataset_source "HuggingFaceFW/fineweb-edu" \
    --dataset_name "sample-10BT" \
    --output_path "edu_fineweb_gpt2" \
    --shard_size 100000000

# Step 2: Update config.yaml
# Set tokenize_on_the_fly: false
# Set train_data_pattern: "edu_fineweb_gpt2/*_train_*.npy"
# Set val_data_pattern: "edu_fineweb_gpt2/*_val_*.npy"
# Set tokenizer_name: "gpt2"

# Step 3: Run training
python retrieval_mlm_configurable.py config.yaml
```

### Custom Tokenizer Usage

```bash
# Step 1: Tokenize with custom tokenizer
python tokenize_npy.py \
    --tokenizer_name "/path/to/custom/tokenizer" \
    --dataset_source "HuggingFaceFW/fineweb-edu" \
    --output_path "edu_fineweb_custom" \
    --shard_size 50000000

# Step 2: Update config.yaml
# Set tokenizer_name: "/path/to/custom/tokenizer"  # Must match!

# Step 3: Run training
python retrieval_mlm_configurable.py config.yaml
```

## 🔧 Command-Line Options

### `tokenize_npy.py` Arguments

| Argument | Default | Description |
|----------|---------|-------------|
| `--tokenizer_name` | `gpt2` | Tokenizer name (tiktoken) or path (custom) |
| `--dataset_source` | `HuggingFaceFW/fineweb-edu` | Dataset source or local path |
| `--dataset_name` | `sample-10BT` | Dataset configuration name |
| `--output_path` | `tokenized_data` | Output directory for .npy files |
| `--shard_size` | `100000000` | Tokens per shard (100M default) |
| `--sequence_length` | `1024` | Sequence length (for future use) |
| `--num_processes` | `auto` | Number of processes for tokenization |

## 📁 File Structure

After tokenization, you'll have:
```
output_path/
├── dataset_train_000000.npy
├── dataset_train_000001.npy
├── ...
├── dataset_val_000000.npy
└── tokenization_info.txt
```

## ⚠️ Important Notes

### Tokenizer Consistency
**CRITICAL**: The `tokenizer_name` in `config.yaml` MUST match the tokenizer used in `tokenize_npy.py`. Mismatches will cause training issues.

### Vocab Size Handling
- **tiktoken**: Base vocab size + 4 special tokens (MASK, PAD, CLS, EOS)
- **Custom tokenizers**: Uses existing special tokens when available
- **Data types**: uint16 for vocab < 65536, uint32 for larger vocabs

### Validation Features
- Automatic tokenizer compatibility checking
- Token range validation
- Clear warning messages for mismatches
- Tokenization info files for debugging

## 🧪 Testing

Run the test suite to verify everything works:

```bash
# Activate the conda environment
conda activate llm_env

# Run comprehensive tests
python test_pipeline.py

# View usage examples
python pipeline_usage_examples.py
```

## 🔍 Troubleshooting

### Common Issues

1. **No .npy files found**
   - Check `train_data_pattern` and `val_data_pattern` in config.yaml
   - Verify tokenization completed successfully
   - Ensure paths match between tokenization and training

2. **Token ID out of range**
   - Verify `tokenizer_name` matches between tokenization and training
   - Check `tokenization_info.txt` for vocab size details
   - Ensure no tokenizer version mismatches

3. **Memory issues during tokenization**
   - Reduce `--shard_size` parameter
   - Reduce `--num_processes` parameter
   - Process dataset in smaller chunks

4. **Slow tokenization**
   - Increase `--num_processes` (up to CPU count)
   - Use faster storage (SSD vs HDD)
   - Consider smaller datasets for testing

## 📊 Performance Tips

- **Shard sizes**: 50-100M tokens per shard for optimal loading
- **Multiprocessing**: Use up to CPU count for tokenization
- **Storage**: Use fast storage for better I/O performance
- **Memory**: Monitor memory usage with large datasets

## 🎯 Next Steps

The enhanced pipeline provides a solid foundation for flexible tokenization and training. Future improvements could include:

- Streaming tokenization for very large datasets
- Automatic tokenizer detection from .npy files
- Distributed tokenization across multiple machines
- Integration with more tokenizer formats

---

For more detailed examples and advanced usage, see `pipeline_usage_examples.py`.
