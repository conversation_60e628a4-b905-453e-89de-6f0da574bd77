#!/usr/bin/env python3
"""
Test script for the enhanced tokenization and data loading pipeline.
Tests both tiktoken and custom tokenizer compatibility.
"""

import os
import sys
import tempfile
import shutil
import subprocess
import yaml
from pathlib import Path

def create_test_dataset(temp_dir):
    """Create a small test dataset for testing"""
    import json
    from datasets import Dataset

    # Create a small test dataset
    test_data = {
        "text": [
            "This is a test document for tokenization. It contains multiple sentences.",
            "Another test document with different content. This helps verify the tokenizer works correctly.",
            "A third document to ensure we have enough data for testing the pipeline functionality.",
            "Final test document with some technical terms like tokenization, embeddings, and transformers.",
            "Last document to complete our small test dataset for validation purposes."
        ]
    }

    dataset = Dataset.from_dict(test_data)
    dataset_path = os.path.join(temp_dir, "test_dataset")
    dataset.save_to_disk(dataset_path)
    return dataset_path

def test_tokenize_npy_basic_persistent(temp_dir):
    """Test basic functionality of tokenize_npy.py with tiktoken"""
    print("=" * 60)
    print("Testing tokenize_npy.py with tiktoken (gpt2)")
    print("=" * 60)

    # Create test dataset
    dataset_path = create_test_dataset(temp_dir)
    output_path = os.path.join(temp_dir, "test_tokenized")

    # Test with minimal dataset (just a few examples)
    cmd = [
        sys.executable, "tokenize_npy.py",
        "--tokenizer_name", "gpt2",
        "--dataset_source", dataset_path,
        "--output_path", output_path,
        "--shard_size", "100",  # Very small for testing
    ]

    print(f"Running command: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        print("STDOUT:")
        print(result.stdout)
        if result.stderr:
            print("STDERR:")
            print(result.stderr)

        if result.returncode == 0:
            print("✅ tokenize_npy.py completed successfully")

            # Check if files were created
            npy_files = list(Path(output_path).glob("*.npy"))
            info_file = Path(output_path) / "tokenization_info.txt"

            print(f"Created {len(npy_files)} .npy files")
            print(f"Files created: {[f.name for f in npy_files]}")
            print(f"Info file exists: {info_file.exists()}")

            if info_file.exists():
                print("Tokenization info:")
                print(info_file.read_text())

            return True, output_path
        else:
            print(f"❌ tokenize_npy.py failed with return code {result.returncode}")
            return False, None

    except subprocess.TimeoutExpired:
        print("❌ tokenize_npy.py timed out")
        return False, None
    except Exception as e:
        print(f"❌ Error running tokenize_npy.py: {e}")
        return False, None

def test_config_npy_mode(tokenized_path):
    """Test loading .npy files with retrieval_mlm_configurable.py"""
    print("=" * 60)
    print("Testing .npy loading in retrieval_mlm_configurable.py")
    print("=" * 60)
    
    # Create a test config
    test_config = {
        'model': {
            'block_size': 1024,
            'n_layer': 2,  # Small for testing
            'n_head': 4,
            'n_embd': 128,
            'n_head_4': 4,
            'mask_prob': 0.3,
            'time_mixing': {
                'd_mix_lora_attention': 8,
                'd_mix_lora_mlp': 8
            },
            'dtype': 'bfloat16',
            'use_compile': False
        },
        'training': {
            'total_batch_size': 1024,
            'micro_batch_size': 2,
            'sequence_length': 1024,
            'max_lr': 1e-4,
            'min_lr_ratio': 0.1,
            'warmup_steps': 10,
            'max_steps': 20,
            'base_learning_rate': 1e-4,
            'weight_decay': 0.1,
            'embedding_lr_scale': 0.1,
            'beta1': 0.9,
            'beta2': 0.95,
            'eps': 1e-8,
            'grad_clip_norm': 1.0,
            'val_eval_interval': 10,
            'val_loss_steps': 2,
            'checkpoint_interval': 100,
            'random_seed': 1337
        },
        'data': {
            'tokenize_on_the_fly': False,
            'train_data_pattern': f"{tokenized_path}/*.npy",  # Use all .npy files
            'val_data_pattern': f"{tokenized_path}/*.npy",
            'tokenizer_name': 'gpt2',
            'mask_prob': 0.3
        },
        'infrastructure': {
            'device_type': 'cpu',  # Use CPU for testing
            'log_dir': '/tmp',
            'log_file_name': 'test_log.txt',
            'verbose_logging': True,
            'tensorboard': {'enabled': False}
        },
        'wandb': {
            'enabled': False
        },
        'advanced': {
            'set_float32_matmul_precision': 'high',
            'ddp': {
                'find_unused_parameters': False,
                'backend': 'nccl'
            },
            'init': {
                'std': 0.02,
                'scale_init_layers': True
            }
        }
    }
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        yaml.dump(test_config, f)
        config_path = f.name
    
    try:
        # Test importing and basic functionality
        print("Testing config loading and data loader initialization...")
        
        # Import the module
        sys.path.insert(0, '.')
        import retrieval_mlm_configurable as mlm
        
        # Load config
        config = mlm.Config(config_path)
        print("✅ Config loaded successfully")
        
        # Get tokenizer info
        tokenizer_info = mlm.get_tokenizer_info(config.get('data.tokenizer_name'))
        print(f"✅ Tokenizer info loaded: vocab_size={tokenizer_info['vocab_size']}")
        
        # Debug: Check what files exist
        import glob
        pattern = f"{tokenized_path}/*.npy"
        found_files = glob.glob(pattern)
        print(f"Debug: Looking for pattern: {pattern}")
        print(f"Debug: Found files: {found_files}")

        # Test data loader
        try:
            data_loader = mlm.DataLoaderLite(
                B=2, T=64, process_rank=0, num_processes=1,
                split='train', config=config, tokenizer_info=tokenizer_info
            )
            print("✅ DataLoader initialized successfully")
            
            # Try to get a batch
            batch_result = data_loader.next_batch()
            inputs, labels, attention_mask = batch_result
            print(f"✅ Generated batch: inputs shape {inputs.shape}, labels shape {labels.shape}")
            print(f"   Attention mask shape: {attention_mask.shape}")
            
            # Check token ranges
            max_input = inputs.max().item()
            min_input = inputs.min().item()
            print(f"   Token range: {min_input} to {max_input}")
            print(f"   Expected vocab size: {tokenizer_info['vocab_size']}")
            
            if max_input < tokenizer_info['vocab_size']:
                print("✅ Token IDs are within expected range")
            else:
                print(f"❌ Token IDs exceed vocab size: {max_input} >= {tokenizer_info['vocab_size']}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error with DataLoader: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ Error testing config: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        os.unlink(config_path)

def main():
    """Run all tests"""
    print("🚀 Starting Enhanced Pipeline Tests")
    print("=" * 60)

    # Use a persistent temp directory for the whole test
    with tempfile.TemporaryDirectory() as temp_dir:
        # Test 1: Basic tokenization with tiktoken
        success, tokenized_path = test_tokenize_npy_basic_persistent(temp_dir)
        if not success:
            print("❌ Basic tokenization test failed")
            return False

        # Test 2: Loading .npy files
        success = test_config_npy_mode(tokenized_path)
        if not success:
            print("❌ .npy loading test failed")
            return False
    
    print("=" * 60)
    print("🎉 All tests passed!")
    print("=" * 60)
    print("The enhanced pipeline is working correctly:")
    print("✅ tokenize_npy.py can tokenize data with different configurations")
    print("✅ retrieval_mlm_configurable.py can load .npy files correctly")
    print("✅ Token compatibility is maintained between tokenization and training")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
